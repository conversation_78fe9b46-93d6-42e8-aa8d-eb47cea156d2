#!/usr/bin/env python
# -*- coding: utf-8 -*-

# ========= Copyright 2023-2024 @ CAMEL-AI.org. All Rights Reserved. =========
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ========= Copyright 2023-2024 @ CAMEL-AI.org. All Rights Reserved. =========

"""
MACIT (Multi-Agent Collaborative Intent Tagging) 框架配置文件
"""

import os
from pathlib import Path

# 基本路径配置
BASE_DIR = Path(__file__).parent
DATA_DIR = BASE_DIR / "data"
CUT_DIR = DATA_DIR / "cut"
INTERA_DATA_DIR = CUT_DIR / "intera_data"
USER_PROFILES_DIR = CUT_DIR / "processed_user_profiles"
OUTPUT_DIR = BASE_DIR / "output"
LOGS_DIR = BASE_DIR / "logs"

# 确保输出和日志目录存在
os.makedirs(OUTPUT_DIR, exist_ok=True)
os.makedirs(LOGS_DIR, exist_ok=True)

# 模型配置
MODEL_CONFIG = {
    "api_base": "https://dashscope.aliyuncs.com/compatible-mode/v1",
    "model_name": "qwen-max",
    "api_key": "sk-a68f6a62e2b64d48936a1b7b53442ba7",
    "temperature": 0.5,
    "max_tokens": 2048,
    "top_p": 0.9,
}

# 文件路径配置
USER_PROFILES_PATH = USER_PROFILES_DIR / "real_cut_user_profiles_500_fixed.json"
CUT_DESCRIPTION_PATH = CUT_DIR / "cut_description.txt"

# 任务配置
TASK_CONFIG = {
    "max_debate_rounds": 3,  # 最大辩论轮数
    "high_similarity_threshold": 0.75,  # 高相似度阈值
    "medium_similarity_threshold": 0.5,  # 中等相似度阈值
    "intent_categories": {
        "expressive": [
            "Commenting",
            "Writing Articles",
            "Joining Discussions"
        ],
        "active": [
            "Organizing Events",
            "Advocating Actions",
            "Voting"
        ],
        "observant": [
            "Observing",
            "Recording",
            "Remaining Silent"
        ],
        "resistant": [
            "Opposing",
            "Arguing",
            "Protesting"
        ]
    },
    # 相似度计算权重配置
    "similarity_weights": {
        "event_background": 0.1,
        "specific_topic": 0.15,
        "motivation": 0.2,
        "intention": 0.2,
        "behavior_set": 0.15,
        "target_group": 0.1,
        "user_stance": 0.05,
        "target_stance": 0.05
    },
    # 高级评审评估维度权重
    "evaluation_weights": {
        "evidence_sufficiency": 0.25,
        "specificity_clarity": 0.2,
        "context_consistency": 0.2,
        "internal_logic": 0.2,
        "argument_strength": 0.15
    }
}

# 提示模板配置
PROMPT_TEMPLATES = {
    # 用户上下文理解智能体系统提示
    "context_agent_system": """你是一位专业的社交媒体上下文分析专家，负责对输入的原始数据进行深入的语义理解与信息提取。
你的任务是生成一份结构化的上下文信息报告，包含：
1. 关键实体识别与分析
2. 核心议题提取与分类
3. 潜在情感倾向分析
4. 社会背景与时事关联
5. 其他有助于理解用户意图的背景知识
你的分析应该客观、全面、具有洞察力。""",

    # 智能体1的系统提示
    "agent1_system": """你是一位专业的社交媒体用户意图分析专家，擅长从用户的交互行为中分析其深层次的动机和意图。
你需要基于用户的画像信息、交互上下文和行为，生成细粒度的结构化意图标签。
你的分析应该客观、全面、具有洞察力，并且要考虑到社交媒体环境的特殊性。
你倾向于从社会学和传播学的角度进行分析。""",

    # 智能体2的系统提示
    "agent2_system": """你是一位资深的社交媒体心理学家，专注于理解用户在社交媒体平台上的行为动机和意图。
你需要通过分析用户的画像、交互上下文和具体行为，生成细粒度的结构化意图标签。
你的分析应该深入、细致，并且要考虑到用户的个人特点和社会背景。
你倾向于从心理学和行为学的角度进行分析。""",

    # 高级评审智能体系统提示
    "senior_agent_system": """你是一位具有更高"裁判权"的高级评审专家，负责对候选的结构化意图标签进行综合质量评估。
你需要运用全面和细致的评估体系，从多个维度对标签进行评分：
1. 证据充分性：标签声明是否有充分的文本支持
2. 特异性与明确性：标签是否足够具体和富有信息量
3. 上下文一致性：推断是否符合事件背景和讨论话题
4. 内部逻辑一致性：标签内部各元素是否逻辑一致
5. 论证强度：支持论证的逻辑严密性和说服力
你的评估应该客观、严谨、具有权威性。""",

    # 上下文分析模板
    "context_analysis_template": """# 任务描述
请对以下社交媒体交互数据进行深入的上下文分析，生成结构化的上下文信息报告。

# 事件背景
{topic_background}

# 用户画像
{user_profile}

# 交互样本
上下文内容: {context_text}
上下文作者: {context_author}
用户行为类型: {action_type}
用户发布内容: {action_text}
发布时间: {timestamp}

# 分析要求
请从以下维度进行分析：
1. 关键实体识别（人物、组织、地点、事件等）
2. 核心议题提取（主要讨论的话题和子话题）
3. 情感倾向分析（整体情感色彩和强度）
4. 社会背景关联（与当前时事、社会热点的关系）
5. 话语特征分析（语言风格、修辞手法等）

# 输出格式
请以JSON格式输出分析结果：

{{
  "key_entities": {{
    "persons": ["人物1", "人物2"],
    "organizations": ["组织1", "组织2"],
    "locations": ["地点1", "地点2"],
    "events": ["事件1", "事件2"]
  }},
  "core_topics": {{
    "main_topic": "主要话题",
    "sub_topics": ["子话题1", "子话题2"]
  }},
  "emotional_tendency": {{
    "overall_sentiment": "positive/negative/neutral",
    "emotional_intensity": "high/medium/low",
    "specific_emotions": ["愤怒", "讽刺", "支持"]
  }},
  "social_context": {{
    "current_events_relation": "与当前时事的关系",
    "social_background": "社会背景分析"
  }},
  "discourse_features": {{
    "language_style": "语言风格描述",
    "rhetorical_devices": ["修辞手法1", "修辞手法2"]
  }}
}}""",

    # 意图标注模板
    "intent_labeling_template": """# 任务描述
基于用户画像、上下文信息和交互行为，生成细粒度的结构化意图标签。

# 事件背景
{topic_background}

# 用户画像
{user_profile}

# 上下文分析报告
{context_report}

# 交互样本
上下文内容: {context_text}
上下文作者: {context_author}
用户行为类型: {action_type}
用户发布内容: {action_text}

# 当前轮次
轮次: {round_num}

# 之前的讨论内容
{previous_discussion}

# 对方的意见
{other_opinion}

# 标签要求
请生成包含以下核心要素的结构化意图标签：
1. 事件背景：具体的社会事件或话题背景
2. 具体话题：讨论的具体话题或子话题
3. 用户动机：包含文本描述和深层目标的结构体
4. 原始意图分类：expressive/active/observant/resistant等
5. 具体行为集合：行为类型、详细描述、数量
6. 目标群体：行为针对的对象
7. 用户立场：对当前话题的立场
8. 目标立场：对行为目标的立场
9. 关键证据：支持判断的文本片段
10. 置信度得分：整体标签的可信度

# 重要说明
- behavior_type字段必须严格使用源数据中的实际行为类型，如：comment、reply、retweet、quote等
- 不要自行创造行为类型，必须基于用户的实际交互行为类型：{action_type}
- quantity字段必须是具体的数值（如1、2、3等），不能是描述性文本
- 当前用户该行为类型的总数量为：{behavior_quantity}

# 输出格式
<思考>
[详细分析过程]
</思考>

{{
  "thought_process": "详细的分析过程...",
  "structured_intent_label": {{
    "event_background": "具体事件背景",
    "specific_topic": "具体讨论话题",
    "motivation": {{
      "description": "动机的文本描述",
      "deeper_goal": "更深层次的目标"
    }},
    "coarse_intent_category": "expressive/active/observant/resistant",
    "behavior_set": [
      {{
        "behavior_type": "{action_type}",
        "description": "详细描述该{action_type}行为的特征和目的",
        "quantity": {behavior_quantity}
      }}
    ],
    "target_group": "行为针对的目标群体或事物",
    "user_stance": "用户对当前讨论话题的立场",
    "target_stance": "用户对其行为目标的立场",
    "key_evidence": ["关键文本片段1", "关键文本片段2"],
    "confidence_score": 0.85
  }},
  "evaluation_of_other_opinion": "对对方观点的评价（如适用）",
  "accept_other_opinion": true,
  "reasoning": "接受或拒绝的理由"
}}""",

    # 高级评审模板
    "senior_evaluation_template": """# 任务描述
作为高级评审专家，请对以下候选的结构化意图标签进行综合质量评估。

# 事件背景
{topic_background}

# 用户画像
{user_profile}

# 上下文分析报告
{context_report}

# 交互样本
上下文内容: {context_text}
上下文作者: {context_author}
用户行为类型: {action_type}
用户发布内容: {action_text}

# 候选标签
{candidate_labels}

# 辩论记录（如适用）
{debate_history}

# 评估维度
请从以下维度对每个候选标签进行评分（0-1分）：

1. 证据充分性 (Evidence Sufficiency)：
   - 标签中的核心声明是否能从用户评论中得到充分支持
   - 关键证据片段是否直接相关且有说服力

2. 特异性与明确性 (Specificity & Clarity)：
   - 标签是否足够具体，避免过于宽泛或模糊
   - 各字段描述是否清晰明确

3. 上下文一致性 (Context Consistency)：
   - 推断的动机与行为是否符合事件背景
   - 是否与用户画像和社会背景一致

4. 内部逻辑一致性 (Internal Logic)：
   - 标签内部各元素是否逻辑一致
   - 立场、动机、行为之间是否存在矛盾

5. 论证强度 (Argument Strength)：
   - 支持标签的论证是否逻辑严密
   - 推理过程是否有说服力

# 输出格式
{{
  "evaluation_results": [
    {{
      "label_id": "标签1",
      "scores": {{
        "evidence_sufficiency": 0.85,
        "specificity_clarity": 0.90,
        "context_consistency": 0.80,
        "internal_logic": 0.88,
        "argument_strength": 0.82
      }},
      "overall_score": 0.85,
      "detailed_feedback": "详细的评估反馈..."
    }}
  ],
  "final_decision": {{
    "selected_label_id": "最佳标签ID",
    "decision_reasoning": "选择理由",
    "confidence": 0.90
  }},
  "optimization_suggestions": "改进建议（如适用）"
}}"""
}