#!/usr/bin/env python
# -*- coding: utf-8 -*-

# ========= Copyright 2023-2024 @ CAMEL-AI.org. All Rights Reserved. =========
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ========= Copyright 2023-2024 @ CAMEL-AI.org. All Rights Reserved. =========

"""
模型接口模块，用于与本地部署的Llama 3.1-8b模型交互
"""

import json
import logging
import os
import re
import requests
from typing import Dict, Any, Optional, List, Tuple

from camel.models import ModelFactory
from camel.types import ModelPlatformType, ModelType
from camel.agents import ChatAgent
from camel.messages import BaseMessage
from camel.models.stub_model import StubModel

from macit_config import MODEL_CONFIG

# 配置日志
logger = logging.getLogger(__name__)


def create_custom_model():
    """
    创建自定义模型

    Returns:
        自定义模型实例
    """

    # 创建自定义模型配置
    model_config = {
            "temperature": MODEL_CONFIG["temperature"],
            "max_tokens": MODEL_CONFIG["max_tokens"],
            "top_p": MODEL_CONFIG["top_p"],
    }

    logger.info(f"使用通义千问平台创建模型，名称: {MODEL_CONFIG['model_name']}, URL: {MODEL_CONFIG['api_base']}")
    logger.info(f"模型配置: {model_config}")

    model = ModelFactory.create(
        model_platform=ModelPlatformType.OPENAI_COMPATIBLE_MODEL,
        model_type=MODEL_CONFIG["model_name"],
        url=MODEL_CONFIG["api_base"],
        api_key=MODEL_CONFIG["api_key"]
    )

    logger.info(f"成功创建自定义模型: {MODEL_CONFIG['model_name']} 使用通义千问平台，URL: {MODEL_CONFIG['api_base']}")
    return model


def create_agent(system_message: str, agent_id: str) -> ChatAgent:
    """
    创建智能体

    Args:
        system_message: 系统消息
        agent_id: 智能体ID

    Returns:
        ChatAgent实例
    """
    model = create_custom_model()

    # 创建系统消息
    system_msg = BaseMessage.make_assistant_message(
        role_name="Assistant",
        content=system_message,
    )

    try:
        # 尝试使用agent_id参数创建智能体
        agent = ChatAgent(
            system_message=system_msg,
            model=model,
            agent_id=agent_id,
        )
    except TypeError:
        # 如果不支持agent_id参数，则不使用该参数
        logger.warning("ChatAgent不支持agent_id参数，使用默认ID")
        agent = ChatAgent(
            system_message=system_msg,
            model=model,
        )

    logger.info(f"成功创建智能体: {agent_id}")
    return agent


def parse_response(response_text: str) -> Tuple[str, Dict]:
    """
    解析模型响应，提取思考过程和JSON格式的结果

    Args:
        response_text: 模型响应文本

    Returns:
        思考过程和解析后的JSON对象
    """
    # 提取思考过程
    thought_match = re.search(r'<思考>(.*?)</思考>', response_text, re.DOTALL)
    thought_process = thought_match.group(1).strip() if thought_match else ""

    # 提取JSON部分
    json_pattern = r'({[\s\S]*})'
    json_match = re.search(json_pattern, response_text)

    if not json_match:
        logger.error(f"无法从响应中提取JSON: {response_text}")
        return thought_process, {}

    json_str = json_match.group(1)

    try:
        result = json.loads(json_str)
        logger.info("成功解析JSON响应")
        return thought_process, result
    except json.JSONDecodeError as e:
        logger.error(f"JSON解析错误: {e}, 原始JSON: {json_str}")
        # 尝试修复常见的JSON格式问题
        try:
            # 替换单引号为双引号
            json_str = json_str.replace("'", '"')
            # 替换True/False为true/false
            json_str = json_str.replace("True", "true").replace("False", "false")
            result = json.loads(json_str)
            logger.info("修复后成功解析JSON响应")
            return thought_process, result
        except json.JSONDecodeError:
            logger.error("修复后仍然无法解析JSON")
            return thought_process, {}


def calculate_text_similarity(text1: str, text2: str) -> float:
    """
    计算两个文本之间的相似度（基于词汇重叠）

    Args:
        text1: 第一个文本
        text2: 第二个文本

    Returns:
        相似度得分 (0-1)
    """
    if not text1 or not text2:
        return 0.0

    # 确保输入是字符串
    text1 = str(text1) if not isinstance(text1, str) else text1
    text2 = str(text2) if not isinstance(text2, str) else text2

    # 转换为小写并分词
    words1 = set(text1.lower().split())
    words2 = set(text2.lower().split())

    if not words1 or not words2:
        return 0.0

    # 计算Jaccard相似度
    intersection = len(words1.intersection(words2))
    union = len(words1.union(words2))

    return intersection / union if union > 0 else 0.0


def calculate_set_similarity(set1: list, set2: list) -> float:
    """
    计算两个集合之间的相似度

    Args:
        set1: 第一个集合
        set2: 第二个集合

    Returns:
        相似度得分 (0-1)
    """
    if not set1 or not set2:
        return 0.0

    # 转换为集合
    s1 = set(str(item).lower() for item in set1)
    s2 = set(str(item).lower() for item in set2)

    # 计算Jaccard相似度
    intersection = len(s1.intersection(s2))
    union = len(s1.union(s2))

    return intersection / union if union > 0 else 0.0


def calculate_structured_similarity(label1: Dict, label2: Dict) -> float:
    """
    计算两个结构化意图标签之间的相似度

    Args:
        label1: 第一个结构化意图标签
        label2: 第二个结构化意图标签

    Returns:
        相似度得分 (0-1)
    """
    from macit_config import TASK_CONFIG

    if not label1 or not label2:
        return 0.0

    weights = TASK_CONFIG["similarity_weights"]
    similarities = {}

    # 计算各字段相似度
    # 事件背景
    similarities["event_background"] = calculate_text_similarity(
        label1.get("event_background", ""),
        label2.get("event_background", "")
    )

    # 具体话题
    similarities["specific_topic"] = calculate_text_similarity(
        label1.get("specific_topic", ""),
        label2.get("specific_topic", "")
    )

    # 动机（考虑描述和深层目标）
    motivation1 = label1.get("motivation", {})
    motivation2 = label2.get("motivation", {})
    if isinstance(motivation1, dict) and isinstance(motivation2, dict):
        desc_sim = calculate_text_similarity(
            motivation1.get("description", ""),
            motivation2.get("description", "")
        )
        goal_sim = calculate_text_similarity(
            motivation1.get("deeper_goal", ""),
            motivation2.get("deeper_goal", "")
        )
        similarities["motivation"] = (desc_sim + goal_sim) / 2
    else:
        similarities["motivation"] = calculate_text_similarity(
            str(motivation1), str(motivation2)
        )

    # 意图（这里假设是文本描述）
    similarities["intention"] = calculate_text_similarity(
        str(label1.get("intention", "")),
        str(label2.get("intention", ""))
    )

    # 行为集合
    behavior1 = label1.get("behavior_set", [])
    behavior2 = label2.get("behavior_set", [])
    if isinstance(behavior1, list) and isinstance(behavior2, list):
        # 提取行为类型和描述进行比较
        behaviors1 = []
        behaviors2 = []
        for b in behavior1:
            if isinstance(b, dict):
                behaviors1.extend([b.get("behavior_type", ""), b.get("description", "")])
            else:
                behaviors1.append(str(b))
        for b in behavior2:
            if isinstance(b, dict):
                behaviors2.extend([b.get("behavior_type", ""), b.get("description", "")])
            else:
                behaviors2.append(str(b))
        similarities["behavior_set"] = calculate_set_similarity(behaviors1, behaviors2)
    else:
        similarities["behavior_set"] = calculate_text_similarity(
            str(behavior1), str(behavior2)
        )

    # 目标群体
    similarities["target_group"] = calculate_text_similarity(
        label1.get("target_group", ""),
        label2.get("target_group", "")
    )

    # 用户立场
    similarities["user_stance"] = calculate_text_similarity(
        label1.get("user_stance", ""),
        label2.get("user_stance", "")
    )

    # 目标立场
    similarities["target_stance"] = calculate_text_similarity(
        label1.get("target_stance", ""),
        label2.get("target_stance", "")
    )

    # 计算加权平均相似度
    total_weight = 0
    weighted_sum = 0

    for field, weight in weights.items():
        if field in similarities:
            weighted_sum += similarities[field] * weight
            total_weight += weight

    return weighted_sum / total_weight if total_weight > 0 else 0.0


def calculate_similarity(intent1: Dict, intent2: Dict) -> float:
    """
    计算两个意图之间的相似度（兼容旧接口）

    Args:
        intent1: 第一个意图
        intent2: 第二个意图

    Returns:
        相似度得分 (0-1)
    """
    # 检查是否为新的结构化标签格式
    if ("event_background" in intent1 or "specific_topic" in intent1 or
        "event_background" in intent2 or "specific_topic" in intent2):
        return calculate_structured_similarity(intent1, intent2)

    # 兼容旧格式
    if not intent1 or not intent2:
        return 0.0

    # 提取关键字段
    fields = ["motivation", "intention", "behavior"]

    # 计算每个字段的相似度
    similarities = []
    for field in fields:
        text1 = str(intent1.get(field, "")).lower()
        text2 = str(intent2.get(field, "")).lower()
        similarities.append(calculate_text_similarity(text1, text2))

    # 返回平均相似度
    return sum(similarities) / len(similarities) if similarities else 0.0


def evaluate_label_quality(label: Dict, context: Dict) -> Dict:
    """
    评估单个结构化意图标签的质量

    Args:
        label: 结构化意图标签
        context: 评估上下文（包含用户评论、画像等）

    Returns:
        评估结果字典
    """
    from macit_config import TASK_CONFIG

    weights = TASK_CONFIG["evaluation_weights"]
    scores = {}

    # 1. 证据充分性评估
    evidence_score = evaluate_evidence_sufficiency(label, context)
    scores["evidence_sufficiency"] = evidence_score

    # 2. 特异性与明确性评估
    specificity_score = evaluate_specificity_clarity(label)
    scores["specificity_clarity"] = specificity_score

    # 3. 上下文一致性评估
    context_score = evaluate_context_consistency(label, context)
    scores["context_consistency"] = context_score

    # 4. 内部逻辑一致性评估
    logic_score = evaluate_internal_logic(label)
    scores["internal_logic"] = logic_score

    # 5. 论证强度评估（如果有辩论记录）
    argument_score = evaluate_argument_strength(label, context.get("debate_history", []))
    scores["argument_strength"] = argument_score

    # 计算总体得分
    overall_score = sum(scores[dim] * weights[dim] for dim in weights if dim in scores)

    return {
        "scores": scores,
        "overall_score": overall_score
    }


def evaluate_evidence_sufficiency(label: Dict, context: Dict) -> float:
    """评估证据充分性"""
    user_text = context.get("user_text", "")
    key_evidence = label.get("key_evidence", [])

    if not user_text or not key_evidence:
        return 0.0

    # 检查关键证据是否在用户文本中
    evidence_found = 0
    for evidence in key_evidence:
        if evidence and evidence.lower() in user_text.lower():
            evidence_found += 1

    # 基础得分：证据覆盖率
    coverage_score = evidence_found / len(key_evidence) if key_evidence else 0.0

    # 额外检查：动机和行为是否有文本支持
    motivation_desc = ""
    if isinstance(label.get("motivation"), dict):
        motivation_desc = label["motivation"].get("description", "")
    else:
        motivation_desc = str(label.get("motivation", ""))

    # 简单的关键词匹配检查
    support_score = 0.0
    if motivation_desc:
        # 提取关键词并检查是否在用户文本中有体现
        motivation_words = set(motivation_desc.lower().split())
        user_words = set(user_text.lower().split())
        overlap = len(motivation_words.intersection(user_words))
        support_score = min(overlap / len(motivation_words), 1.0) if motivation_words else 0.0

    return (coverage_score * 0.7 + support_score * 0.3)


def evaluate_specificity_clarity(label: Dict) -> float:
    """评估特异性与明确性"""
    score = 0.0
    total_fields = 0

    # 检查各字段的具体性
    fields_to_check = [
        "event_background", "specific_topic", "target_group",
        "user_stance", "target_stance"
    ]

    for field in fields_to_check:
        value = label.get(field, "")
        if value:
            total_fields += 1
            # 简单的长度和具体性检查
            if len(str(value)) > 10 and not any(vague in str(value).lower()
                                               for vague in ["一般", "普通", "某种", "可能"]):
                score += 1

    # 检查动机的具体性
    motivation = label.get("motivation", {})
    if isinstance(motivation, dict):
        if motivation.get("description") and motivation.get("deeper_goal"):
            total_fields += 1
            if (len(motivation["description"]) > 10 and
                len(motivation["deeper_goal"]) > 5):
                score += 1

    # 检查行为集合的具体性
    behavior_set = label.get("behavior_set", [])
    if behavior_set:
        total_fields += 1
        specific_behaviors = 0
        for behavior in behavior_set:
            if isinstance(behavior, dict):
                if (behavior.get("behavior_type") and
                    behavior.get("description") and
                    len(behavior["description"]) > 10):
                    specific_behaviors += 1
        if specific_behaviors > 0:
            score += specific_behaviors / len(behavior_set)

    return score / total_fields if total_fields > 0 else 0.0


def evaluate_context_consistency(label: Dict, context: Dict) -> float:
    """评估上下文一致性"""
    # 这里可以实现更复杂的一致性检查
    # 目前使用简化版本

    # 检查事件背景是否与话题背景一致
    event_bg = label.get("event_background", "").lower()
    topic_bg = context.get("topic_background", "").lower()

    bg_consistency = 0.0
    if event_bg and topic_bg:
        # 简单的关键词重叠检查
        event_words = set(event_bg.split())
        topic_words = set(topic_bg.split())
        overlap = len(event_words.intersection(topic_words))
        bg_consistency = min(overlap / len(event_words), 1.0) if event_words else 0.0

    # 检查用户立场与用户画像的一致性
    user_stance = label.get("user_stance", "").lower()
    user_profile = context.get("user_profile", {})

    stance_consistency = 0.5  # 默认中等一致性
    if user_stance and user_profile:
        # 检查与用户认知特征的一致性
        cognitive_profile = user_profile.get("cognitive_profile", {})
        if cognitive_profile:
            stance_type = cognitive_profile.get("stance", {}).get("type", "")
            if stance_type:
                # 简单的一致性检查
                if ("支持" in user_stance and stance_type in ["conservative", "neutral"]) or \
                   ("反对" in user_stance and stance_type in ["radical", "liberal"]):
                    stance_consistency = 0.8

    return (bg_consistency * 0.6 + stance_consistency * 0.4)


def evaluate_internal_logic(label: Dict) -> float:
    """评估内部逻辑一致性"""
    # 检查动机、行为和立场之间的逻辑一致性

    motivation = label.get("motivation", {})
    behavior_set = label.get("behavior_set", [])
    user_stance = label.get("user_stance", "").lower()
    target_stance = label.get("target_stance", "").lower()

    consistency_score = 0.0
    checks = 0

    # 检查动机与行为的一致性
    if motivation and behavior_set:
        checks += 1
        motivation_text = ""
        if isinstance(motivation, dict):
            motivation_text = (motivation.get("description", "") + " " +
                             motivation.get("deeper_goal", "")).lower()
        else:
            motivation_text = str(motivation).lower()

        # 检查行为是否与动机匹配
        behavior_match = False
        for behavior in behavior_set:
            if isinstance(behavior, dict):
                behavior_desc = behavior.get("description", "").lower()
                # 简单的关键词匹配
                if any(word in behavior_desc for word in motivation_text.split() if len(word) > 3):
                    behavior_match = True
                    break

        if behavior_match:
            consistency_score += 1

    # 检查立场的一致性
    if user_stance and target_stance:
        checks += 1
        # 检查用户立场和目标立场是否逻辑一致
        if (("支持" in user_stance and "支持" in target_stance) or
            ("反对" in user_stance and "反对" in target_stance) or
            ("中立" in user_stance and "中立" in target_stance)):
            consistency_score += 1
        elif (("支持" in user_stance and "反对" in target_stance) or
              ("反对" in user_stance and "支持" in target_stance)):
            # 这种情况可能是合理的（比如支持某个话题但反对某个具体做法）
            consistency_score += 0.7

    return consistency_score / checks if checks > 0 else 0.5


def evaluate_argument_strength(label: Dict, debate_history: list) -> float:
    """评估论证强度"""
    if not debate_history:
        return 0.5  # 没有辩论记录时给中等分

    # 分析辩论过程中的论证质量
    argument_quality = 0.0
    total_rounds = len(debate_history)

    for round_data in debate_history:
        round_score = 0.0

        # 检查论证的详细程度
        for agent_key in ["agent1", "agent2"]:
            if agent_key in round_data:
                thought = round_data[agent_key].get("thought", "")
                result = round_data[agent_key].get("result", {})

                # 评估思考过程的深度
                if thought and len(thought) > 100:
                    round_score += 0.3

                # 评估分析的结构化程度
                if result.get("thought_process") and len(result["thought_process"]) > 50:
                    round_score += 0.2

                # 评估是否有具体的评价和理由
                if result.get("evaluation_of_other_opinion"):
                    round_score += 0.3

                if result.get("reasoning"):
                    round_score += 0.2

        argument_quality += round_score / 2  # 平均两个智能体的得分

    return argument_quality / total_rounds if total_rounds > 0 else 0.5